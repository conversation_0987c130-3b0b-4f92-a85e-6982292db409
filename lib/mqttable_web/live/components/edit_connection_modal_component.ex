defmodule MqttableWeb.EditConnectionModalComponent do
  @moduledoc """
  LiveComponent for rendering the edit connection modal.
  This component is used to edit an existing connection within a connection set.
  """
  use MqttableWeb, :live_component
  alias MqttableWeb.ConnectionFormComponent
  alias MqttableWeb.Utils.ConnectionValidation

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h3 class="text-xl font-semibold mb-4 text-center">Edit Client</h3>
      <.form
        for={%{}}
        as={:connection}
        phx-submit="update_connection"
        phx-change="validate"
        phx-target={@myself}
      >
        <input type="hidden" name="old_client_id" value={@edit_connection.client_id} />
        <div class="connection-form">
          <ConnectionFormComponent.connection_header connection_set={@connection_set} />

          <div class="space-y-4">
            <!-- General Section (Always visible) -->
            <ConnectionFormComponent.general_section
              edit_connection={@edit_connection}
              connection_set={@connection_set}
              validation_errors={@validation_errors}
            />
            
    <!-- Advanced Section -->
            <ConnectionFormComponent.advanced_section edit_connection={@edit_connection} />
            
    <!-- User Properties Section - Only for MQTT 5.0 -->
            <%= if @edit_connection.mqtt_version == "5.0" do %>
              <ConnectionFormComponent.user_properties_section edit_connection={@edit_connection} />
            <% end %>
            
    <!-- Last Will and Testament Section -->
            <ConnectionFormComponent.lwt_section edit_connection={@edit_connection} />

            <div class="flex justify-between space-x-2 pt-3">
              <button
                type="button"
                phx-click="delete_connection"
                phx-value-set_name={@connection_set.name}
                phx-value-client_id={@edit_connection.client_id}
                class="btn btn-ghost text-error"
              >
                <.icon name="hero-trash" class="h-4 w-4 mr-1" /> Delete
              </button>
              <div class="flex space-x-2">
                <button type="button" phx-click="close_modal" class="btn">Cancel</button>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </div>
        </div>
      </.form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:validation_errors, %{})

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"connection" => connection_params}, socket) do
    # Get existing connections from the current connection set
    existing_connections = Map.get(socket.assigns.connection_set, :connections, [])

    # Get the current connection's client_id to exclude it from validation
    current_client_id = socket.assigns.edit_connection.client_id

    # Validate the connection parameters (excluding current connection)
    case ConnectionValidation.validate_connection_uniqueness(
           connection_params,
           existing_connections,
           current_client_id
         ) do
      {:ok, _} ->
        {:noreply, assign(socket, :validation_errors, %{})}

      {:error, errors} ->
        {:noreply, assign(socket, :validation_errors, errors)}
    end
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "mqtt_version_changed",
        %{"connection" => %{"mqtt_version" => mqtt_version}},
        socket
      ) do
    # Update the edit_connection in the socket with the new MQTT version
    edit_connection = Map.put(socket.assigns.edit_connection, :mqtt_version, mqtt_version)

    {:noreply, assign(socket, :edit_connection, edit_connection)}
  end

  @impl true
  def handle_event(
        "update_connection",
        %{"old_client_id" => old_client_id, "connection" => connection_params},
        socket
      ) do
    # Send the updated params to the parent LiveView
    send(self(), {:update_connection, old_client_id, connection_params})

    {:noreply, socket}
  end
end
