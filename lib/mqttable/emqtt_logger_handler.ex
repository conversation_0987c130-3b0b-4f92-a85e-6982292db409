defmodule Mqttable.EmqttLoggerHandler do
  @moduledoc """
  Custom Logger Handler to capture emqtt module's send_data and recv_data logs
  """

  require Logger
  alias Phoenix.PubSub

  @behaviour :logger_handler

  # Handler configuration
  @handler_id :mqtt_trace_logger_handler
  @pubsub_topic "mqtt_trace"

  @doc """
  Start the Logger Handler
  """
  def start_handler(_opts \\ []) do
    # Define a filter function that only allows logs from the emqtt module
    emqtt_filter = {&__MODULE__.filter_emqtt_only/2, []}

    config = %{
      level: :debug,
      # Default to stopping logs that don't match our filter
      filter_default: :stop,
      # Add our custom filter
      filters: [{:emqtt_only, emqtt_filter}]
    }

    case :logger.add_handler(@handler_id, __MODULE__, config) do
      :ok ->
        Logger.info("EMQTT Trace Logger Handler started successfully")
        :ok

      {:error, {:already_exist, _}} ->
        Logger.info("EMQTT Trace Logger Handler already exists")
        :ok

      {:error, reason} ->
        Logger.error("Failed to start EMQTT Trace Logger Handler: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Stop the Logger Handler
  """
  def stop_handler do
    case :logger.remove_handler(@handler_id) do
      :ok ->
        Logger.info("EMQTT Logger Trace Handler stopped")
        :ok

      {:error, reason} ->
        Logger.error("Failed to stop EMQTT Trace Logger Handler: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Check if Handler is running
  """
  def handler_running? do
    handlers = :logger.get_handler_ids()
    @handler_id in handlers
  end

  @doc """
  Subscribe to MQTT trace messages
  """
  def subscribe do
    PubSub.subscribe(Mqttable.PubSub, @pubsub_topic)
  end

  # Logger Handler callback
  @impl :logger_handler
  def log(log_event, config) do
    try do
      handle_log_event(log_event, config)
    catch
      type, reason ->
        stacktrace = __STACKTRACE__
        Logger.error("Error: #{type} #{reason}")
        Logger.error("Stacktrace:\n#{Exception.format_stacktrace(stacktrace)}")
        :ok
    end
  end

  # Handle log events
  defp handle_log_event(%{msg: {:string, _msg}, level: _level, meta: _meta}, _config) do
    # Ignore string messages
    :ok
  end

  defp handle_log_event(%{msg: {:report, report_data}, level: _level, meta: _meta}, _config) do
    process_emqtt_report(report_data)
  end

  defp handle_log_event(_other_event, _config) do
    # Ignore other log events
    :ok
  end

  # Process emqtt report logs
  defp process_emqtt_report(%{msg: ~c"send_data"} = report) do
    packet = Map.get(report, :packet)
    client_id = Map.get(report, :clientid, "unknown")

    # Process the captured data
    process_send_data(packet, client_id)
  end

  defp process_emqtt_report(%{msg: ~c"send_data_failed"} = report) do
    reason = Map.get(report, :reason)
    packet = Map.get(report, :packet)
    client_id = Map.get(report, :clientid, "unknown")

    # Process the failed data
    process_send_data_failed(reason, packet, client_id)
  end

  defp process_emqtt_report(%{msg: ~c"recv_data"} = report) do
    data = Map.get(report, :data)
    client_id = Map.get(report, :clientid, "unknown")

    # Process the received data
    parse_state = Mqttable.MqttClient.Manager.lookup_client_parse_state(client_id)
    process_recv_data(data, parse_state, client_id)
  end

  defp process_emqtt_report(_other_report) do
    # Ignore other emqtt reports
    :ok
  end

  # Process send data
  defp process_send_data(packet, client_id) do
    process_mqtt_packet(packet, client_id, "OUT")
  end

  # Process failed send data
  defp process_send_data_failed(reason, _packet, client_id) do
    Logger.error("EMQTT Send Data Failed from client #{client_id}: #{inspect(reason)}")
  end

  # Process received data
  defp process_recv_data(data, parse_state, client_id) do
    try do
      case :emqtt_frame.parse(data, parse_state) do
        {:ok, packet, "", new_parse_state} ->
          process_mqtt_packet(packet, client_id, "IN")
          Mqttable.MqttClient.Manager.store_client_parse_state(client_id, new_parse_state)

        {:ok, packet, rest, new_parse_state} ->
          process_mqtt_packet(packet, client_id, "IN")
          process_recv_data(rest, new_parse_state, client_id)

        {:more, new_parse_state} ->
          Mqttable.MqttClient.Manager.store_client_parse_state(client_id, new_parse_state)
      end
    catch
      :exit, reason ->
        Logger.error("EMQTT Receive Data Failed from client #{client_id}: #{inspect(reason)}")
    end
  end

  # Process MQTT packet and forward to trace component for all packet types
  defp process_mqtt_packet(packet, client_id, direction) do
    case packet do
      # Match CONNECT packet
      {:mqtt_packet, {:mqtt_packet_header, 1, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "CONNECT",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match CONNACK packet
      {:mqtt_packet, {:mqtt_packet_header, 2, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "CONNACK",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PUBLISH packet
      {:mqtt_packet, {:mqtt_packet_header, 3, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PUBLISH",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PUBACK packet
      {:mqtt_packet, {:mqtt_packet_header, 4, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PUBACK",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PUBREC packet
      {:mqtt_packet, {:mqtt_packet_header, 5, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PUBREC",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PUBREL packet
      {:mqtt_packet, {:mqtt_packet_header, 6, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PUBREL",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PUBCOMP packet
      {:mqtt_packet, {:mqtt_packet_header, 7, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PUBCOMP",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match SUBSCRIBE packet
      {:mqtt_packet, {:mqtt_packet_header, 8, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "SUBSCRIBE",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match SUBACK packet
      {:mqtt_packet, {:mqtt_packet_header, 9, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "SUBACK",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match UNSUBSCRIBE packet
      {:mqtt_packet, {:mqtt_packet_header, 10, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "UNSUBSCRIBE",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match UNSUBACK packet
      {:mqtt_packet, {:mqtt_packet_header, 11, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "UNSUBACK",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PINGREQ packet
      {:mqtt_packet, {:mqtt_packet_header, 12, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PINGREQ",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match PINGRESP packet
      {:mqtt_packet, {:mqtt_packet_header, 13, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "PINGRESP",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match DISCONNECT packet
      {:mqtt_packet, {:mqtt_packet_header, 14, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "DISCONNECT",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # Match AUTH packet
      {:mqtt_packet, {:mqtt_packet_header, 15, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          client_id,
          "AUTH",
          qos,
          retain,
          variable,
          payload,
          direction
        )

      # For unknown packet types
      _ ->
        :ok
    end
  end

  # Format and broadcast trace message for all packet types
  defp format_and_broadcast_trace_message(
         client_id,
         packet_type,
         qos,
         retain,
         variable,
         payload,
         direction
       ) do
    # Extract packet-specific information
    {topic, packet_id, properties, reason_code, extra_info} =
      extract_packet_info(packet_type, variable)

    # Format payload
    formatted_payload = format_payload(payload)

    # Format the message for the trace component
    trace_message = %{
      id: System.unique_integer([:positive, :monotonic]),
      timestamp: format_timestamp(DateTime.utc_now()),
      client_id: client_id,
      type: packet_type,
      direction: direction,
      topic: topic,
      payload: formatted_payload,
      payload_length: byte_size(formatted_payload),
      qos: format_qos(qos, packet_type),
      retain: retain,
      packet_id: packet_id,
      properties: properties,
      reason_code: reason_code,
      extra_info: extra_info
    }

    # Store the message in the appropriate broker's ETS table
    Mqttable.TraceManager.store_message_by_client_id(client_id, trace_message)

    # Also broadcast the trace message for real-time updates
    PubSub.broadcast(Mqttable.PubSub, @pubsub_topic, {:mqtt_trace_message, trace_message})
  end

  # Extract packet-specific information based on packet type and variable part
  defp extract_packet_info(packet_type, variable) do
    case {packet_type, variable} do
      # PUBLISH packet
      {"PUBLISH", {:mqtt_packet_publish, topic, packet_id, properties}} ->
        {topic, packet_id, properties, nil, nil}

      # SUBSCRIBE packet
      {"SUBSCRIBE", {:mqtt_packet_subscribe, packet_id, properties, topic_filters}} ->
        topic = format_topic_filters(topic_filters)
        {topic, packet_id, properties, nil, topic_filters}

      # UNSUBSCRIBE packet
      {"UNSUBSCRIBE", {:mqtt_packet_unsubscribe, packet_id, properties, topic_filters}} ->
        topic = format_topic_filters(topic_filters)
        {topic, packet_id, properties, nil, topic_filters}

      # SUBACK packet
      {"SUBACK", {:mqtt_packet_suback, packet_id, properties, reason_codes}} ->
        {"", packet_id, properties, reason_codes, nil}

      # UNSUBACK packet
      {"UNSUBACK", {:mqtt_packet_unsuback, packet_id, properties, reason_codes}} ->
        {"", packet_id, properties, reason_codes, nil}

      # PUBACK, PUBREC, PUBREL, PUBCOMP packets
      {type, {:mqtt_packet_puback, packet_id, reason_code, properties}}
      when type in ["PUBACK", "PUBREC", "PUBREL", "PUBCOMP"] ->
        {"", packet_id, properties, reason_code, nil}

      # CONNACK packet
      {"CONNACK", {:mqtt_packet_connack, ack_flags, reason_code, properties}} ->
        {"", nil, properties, reason_code, ack_flags}

      # DISCONNECT packet
      {"DISCONNECT", {:mqtt_packet_disconnect, reason_code, properties}} ->
        {"", nil, properties, reason_code, nil}

      # AUTH packet
      {"AUTH", {:mqtt_packet_auth, reason_code, properties}} ->
        {"", nil, properties, reason_code, nil}

      # CONNECT packet
      {"CONNECT",
       {:mqtt_packet_connect, proto_name, proto_ver, is_bridge, clean_start, will_flag, will_qos,
        will_retain, keepalive, properties, clientid, will_props, will_topic, will_payload,
        username, password}} ->
        connect_info = %{
          proto_name: proto_name,
          proto_ver: proto_ver,
          is_bridge: is_bridge,
          clean_start: clean_start,
          will_flag: will_flag,
          will_qos: will_qos,
          will_retain: will_retain,
          keepalive: keepalive,
          clientid: clientid,
          will_props: will_props,
          will_topic: will_topic,
          will_payload: will_payload,
          username: username,
          password: password
        }

        {"", nil, properties, nil, connect_info}

      # For packets with simple variable part or unknown structure
      _ ->
        {"", nil, nil, nil, nil}
    end
  end

  # Format topic filters for SUBSCRIBE/UNSUBSCRIBE packets
  defp format_topic_filters(topic_filters) when is_list(topic_filters) do
    topic_filters
    |> Enum.map(fn
      {topic, _opts} -> topic
      topic when is_binary(topic) -> topic
      _ -> "unknown"
    end)
    |> Enum.join(", ")
  end

  defp format_topic_filters(_), do: ""

  # Format payload for display
  defp format_payload(payload) when is_binary(payload), do: payload
  defp format_payload(nil), do: ""
  defp format_payload(_), do: ""

  # Format QoS for display
  defp format_qos(qos, packet_type) when packet_type in ["PUBLISH", "PUBREL"] and is_integer(qos),
    do: qos

  defp format_qos(_, _), do: "-"

  # Format timestamp for display
  defp format_timestamp(datetime) do
    # Convert UTC to timezone
    datetime
    |> DateTime.shift_zone!(Application.get_env(:mqttable, :time_zone))
    |> Calendar.strftime("%m-%d %H:%M:%S.%3f")
    |> String.slice(0..17)
  end

  # Filter function that only allows logs from the emqtt module
  def filter_emqtt_only(log_event, _filter_args) do
    case log_event do
      %{meta: %{mfa: {module, _function, _arity}}} when module == :emqtt ->
        # Allow logs from emqtt module
        log_event

      _ ->
        # Reject logs from other modules
        :stop
    end
  end
end
