defmodule Mqttable.EmqttLogger do
  @moduledoc """
  Module for using the EMQTT Logger Handler
  """

  require Logger

  alias Mqttable.EmqttLoggerHandler

  @doc """
  Start the EMQTT Logger Handler
  """
  def setup do
    Logger.put_module_level(:emqtt, :debug)
    # Start the Logger Handler
    EmqttLoggerHandler.start_handler()
  end

  @doc """
  Stop the EMQTT Logger Handler
  """
  def cleanup do
    # Stop the Logger Handler
    EmqttLoggerHandler.stop_handler()
    Logger.delete_module_level(:emqtt)
  end

  @doc """
  Check if the EMQTT Logger Handler is running
  """
  def status do
    # Check status
    EmqttLoggerHandler.handler_running?()
  end
end
