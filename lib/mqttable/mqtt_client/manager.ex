defmodule Mqttable.MqttClient.Manager do
  @moduledoc """
  Manager for MQTT client connections.
  This module provides functions for creating, connecting, and managing MQTT clients.
  """
  use GenServer
  require Logger

  alias MqttableWeb.Utils.ConnectionHelpers
  alias Phoenix.PubSub
  alias Mqttable.MqttClient.Topic

  # Constants
  @pubsub_topic "mqtt_clients"

  # Client status values
  @status_connected :connected
  @status_disconnected :disconnected
  @status_reconnecting :reconnecting

  # Type definitions
  @type client_id :: String.t()
  @type client_pid :: pid()
  @type client_status :: :connected | :disconnected | :reconnecting
  @type emqtt_status :: atom()
  @type error_reason :: atom() | {atom(), term()}
  @type error_message :: String.t()

  # Client API

  @doc """
  Starts the MQTT client manager.
  """
  @spec start_link(keyword()) :: GenServer.on_start()
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Connects to an MQTT broker using the specified connection parameters.
  Returns {:ok, client_id} if successful, {:error, reason, error_message} otherwise.
  """
  @spec connect(map(), map()) ::
          {:ok, client_id()}
          | {:error, error_reason(), error_message()}
  def connect(connection, broker) do
    GenServer.call(__MODULE__, {:connect, connection, broker})
  end

  @doc """
  Disconnects from an MQTT broker.
  Returns :ok if successful, {:error, reason} otherwise.
  """
  @spec disconnect(client_id()) :: :ok | {:error, :not_connected}
  def disconnect(client_id) do
    GenServer.call(__MODULE__, {:disconnect, client_id})
  end

  @doc """
  Subscribes to an MQTT topic.
  Returns {:ok, properties, reason_codes} if successful, {:error, reason} otherwise.
  """
  @spec subscribe(client_id(), binary(), keyword()) ::
          {:ok, map(), list()} | {:error, :not_connected} | {:error, any(), String.t()}
  def subscribe(client_id, topic, opts \\ []) do
    GenServer.call(__MODULE__, {:subscribe, client_id, topic, opts})
  end

  @doc """
  Unsubscribes from an MQTT topic.
  Returns {:ok, properties, reason_code} if successful, {:error, reason} otherwise.
  """
  @spec unsubscribe(client_id(), binary()) ::
          {:ok, map(), list()} | {:error, :not_connected} | {:error, any(), String.t()}
  def unsubscribe(client_id, topic) do
    GenServer.call(__MODULE__, {:unsubscribe, client_id, topic})
  end

  @doc """
  Publishes a message to an MQTT topic.
  Returns {:ok, packet_id} if successful, {:error, reason} otherwise.
  For QoS 0 messages, packet_id will be 0.
  """
  @spec publish(client_id(), binary(), binary(), keyword()) ::
          {:ok, integer()} | {:error, :not_connected} | {:error, any(), String.t()}
  def publish(client_id, topic, payload, opts \\ []) do
    GenServer.call(__MODULE__, {:publish, client_id, topic, payload, opts})
  end

  @doc """
  Gets the status of an MQTT client.
  Returns :connected, :disconnected, or :reconnecting.
  """
  @spec get_status(client_id()) :: client_status()
  def get_status(client_id) do
    GenServer.call(__MODULE__, {:get_status, client_id})
  end

  @doc """
  Gets all active MQTT clients.
  Returns a map of client_id to client_pid.
  """
  @spec get_all_clients() :: %{client_id() => client_pid()}
  def get_all_clients do
    GenServer.call(__MODULE__, :get_all_clients)
  end

  @doc """
  Gets all connected MQTT clients.
  Returns a list of maps with client information including MQTT version.
  """
  @spec get_connected_clients() :: [
          %{client_id: client_id(), status: client_status(), mqtt_version: String.t()}
        ]
  def get_connected_clients do
    GenServer.call(__MODULE__, :get_connected_clients)
  end

  # Server state
  # We use ETS tables for clients and status
  @type state :: %{}

  # ETS record structure
  # {client_id, client_pid, mqtt_opts, parse_state, status}

  # Server callbacks

  @impl true
  @spec init(keyword()) :: {:ok, state()}
  def init(_opts) do
    # Initialize state
    :erlang.process_flag(:trap_exit, true)

    state = %{}

    # Create ETS table for clients and status
    # Each record will be {client_id, client_pid, mqtt_opts, parse_state, status}
    :ets.new(__MODULE__, [:set, :public, :named_table])

    # Schedule reconnection of previously connected clients
    # We do this after a short delay to ensure the ConnectionSets server is fully initialized
    Process.send_after(self(), :reconnect_saved_clients, 5001)

    {:ok, state}
  end

  # Server callbacks - handle_call functions

  @impl true
  @spec handle_call({:connect, map(), map()}, GenServer.from(), state()) ::
          {:reply, {:ok, client_id()} | {:error, error_reason(), error_message()}, state()}
  def handle_call({:connect, connection, broker}, _from, state) do
    client_id = connection.client_id

    # Check if client is already connected
    case lookup_client_status(client_id) do
      @status_disconnected -> handle_client_connection(connection, broker, client_id, state)
      _ -> {:reply, {:ok, client_id}, state}
    end
  end

  @impl true
  @spec handle_call({:disconnect, client_id()}, GenServer.from(), state()) ::
          {:reply, :ok | {:error, :not_connected}, state()}
  def handle_call({:disconnect, client_id}, _from, state) do
    case lookup_client_record(client_id) do
      {@status_disconnected, _, _} ->
        {:reply, {:error, :not_connected}, state}

      {@status_reconnecting, nil, _} ->
        # Client is in reconnecting state with no active PID
        # Simply remove the record and broadcast disconnected status
        remove_client_record(client_id)
        broadcast_status_change(client_id, @status_disconnected)
        {:reply, :ok, state}

      {_, client_pid, _} when client_pid != nil ->
        # Disconnect the client - our improved do_disconnect always returns :ok
        do_disconnect(client_pid)
        remove_client_record(client_id)

        # Broadcast status change
        broadcast_status_change(client_id, @status_disconnected)

        {:reply, :ok, state}

      {_, nil, _} ->
        # Client has no active PID (shouldn't happen except for reconnecting, but handle it safely)
        remove_client_record(client_id)
        broadcast_status_change(client_id, @status_disconnected)
        {:reply, :ok, state}
    end
  end

  @impl true
  @spec handle_call({:subscribe, client_id(), binary(), keyword()}, GenServer.from(), state()) ::
          {:reply, {:ok, map(), list()} | {:error, :not_connected} | {:error, any(), String.t()},
           state()}
  def handle_call({:subscribe, client_id, topic, opts}, _from, state) do
    case lookup_client_record(client_id) do
      {@status_disconnected, _, _} ->
        {:reply, {:error, :not_connected}, state}

      {_, client_pid, _} ->
        # Default subscription options
        sub_opts = Keyword.get(opts, :sub_opts, [{:qos, 0}])

        # Process subscription options to convert nl and rap values from 0/1 to false/true
        sub_opts = process_subscription_options(sub_opts)

        # Extract subscription identifier if provided
        sub_id = Keyword.get(opts, :id)

        # Prepare properties map with subscription identifier if provided
        props = prepare_subscription_properties(sub_id)

        # Validate topic before subscribing
        case validate_topic_filter(topic) do
          :ok ->
            # Subscribe to the topic
            try do
              case :emqtt.subscribe(client_pid, props, [{topic, sub_opts}]) do
                {:ok, props, reason_codes} ->
                  # Check if the subscription was successful based on reason codes
                  case check_subscription_success(reason_codes) do
                    :ok ->
                      # Broadcast topic subscription
                      broadcast_topic_subscription(client_id, topic, sub_opts, sub_id)
                      {:reply, {:ok, props, reason_codes}, state}

                    {:error, error_message} ->
                      Logger.error("Failed to subscribe to topic #{topic}: #{error_message}")
                      {:reply, {:error, :subscription_rejected, error_message}, state}
                  end

                {:error, reason} ->
                  error_message = format_mqtt_error(reason)

                  Logger.error(
                    "Failed to subscribe to topic #{topic}: #{inspect(reason)} - #{error_message}"
                  )

                  {:reply, {:error, reason, error_message}, state}
              end
            catch
              :exit, reason ->
                error_message = "Subscription failed: #{inspect(reason)}"
                Logger.error(error_message)
                {:reply, {:error, reason, error_message}, state}
            end

          {:error, error_message} ->
            {:reply, {:error, :invalid_topic, error_message}, state}
        end
    end
  end

  @impl true
  @spec handle_call({:unsubscribe, client_id(), binary()}, GenServer.from(), state()) ::
          {:reply, {:ok, map(), list()} | {:error, :not_connected} | {:error, any(), String.t()},
           state()}
  def handle_call({:unsubscribe, client_id, topic}, _from, state) do
    case lookup_client_record(client_id) do
      {@status_disconnected, _, _} ->
        {:reply, {:error, :not_connected}, state}

      {_, client_pid, _} ->
        try do
          case :emqtt.unsubscribe(client_pid, %{}, [topic]) do
            {:ok, props, reason_codes} ->
              # Broadcast topic unsubscription
              broadcast_topic_unsubscription(client_id, topic)
              {:reply, {:ok, props, reason_codes}, state}

            {:error, reason} ->
              error_message = format_mqtt_error(reason)

              Logger.error(
                "Failed to unsubscribe from topic #{topic}: #{inspect(reason)} - #{error_message}"
              )

              {:reply, {:error, reason, error_message}, state}
          end
        catch
          :exit, reason ->
            error_message = "Unsubscription failed: #{inspect(reason)}"
            Logger.error(error_message)
            {:reply, {:error, reason, error_message}, state}
        end
    end
  end

  @impl true
  @spec handle_call(
          {:publish, client_id(), binary(), binary(), keyword()},
          GenServer.from(),
          state()
        ) ::
          {:reply, {:ok, integer()} | {:error, :not_connected} | {:error, any(), String.t()},
           state()}
  def handle_call({:publish, client_id, topic, payload, opts}, _from, state) do
    case lookup_client_record(client_id) do
      {@status_disconnected, _, _} ->
        {:reply, {:error, :not_connected}, state}

      {_, client_pid, _} ->
        # Extract publish options
        qos = Keyword.get(opts, :qos, 0)
        retain = Keyword.get(opts, :retain, false)
        properties = Keyword.get(opts, :properties, %{})

        try do
          case :emqtt.publish(client_pid, topic, properties, payload, [
                 {:qos, qos},
                 {:retain, retain}
               ]) do
            {:ok, packet_id} ->
              Logger.debug(
                "Published message to topic #{inspect(topic)} with packet_id #{inspect(packet_id)}"
              )

              {:reply, {:ok, packet_id}, state}

            :ok ->
              # QoS 0 messages return :ok without packet_id
              Logger.debug("Published QoS 0 message to topic #{topic}")
              {:reply, {:ok, 0}, state}

            {:error, reason} ->
              error_message = format_mqtt_error(reason)

              Logger.error(
                "Failed to publish to topic #{topic}: #{inspect(reason)} - #{error_message}"
              )

              {:reply, {:error, reason, error_message}, state}
          end
        catch
          :exit, reason ->
            error_message = "Publish failed: #{inspect(reason)}"
            Logger.error(error_message)
            {:reply, {:error, reason, error_message}, state}
        end
    end
  end

  @impl true
  @spec handle_call({:get_status, client_id()}, GenServer.from(), state()) ::
          {:reply, client_status(), state()}
  def handle_call({:get_status, client_id}, _from, state) do
    status = lookup_client_status(client_id)
    {:reply, status, state}
  end

  @impl true
  @spec handle_call(:get_all_clients, GenServer.from(), state()) ::
          {:reply, %{client_id() => client_pid()}, state()}
  def handle_call(:get_all_clients, _from, state) do
    # Convert ETS table records to a map of client_id => client_pid
    clients =
      get_all_client_records()
      |> Enum.map(fn {client_id, client_pid, _mqtt_opts, _parse_state, _status} ->
        {client_id, client_pid}
      end)
      |> Enum.into(%{})

    {:reply, clients, state}
  end

  @impl true
  @spec handle_call(:get_connected_clients, GenServer.from(), state()) ::
          {:reply, [%{client_id: client_id(), status: client_status(), mqtt_version: String.t()}],
           state()}
  def handle_call(:get_connected_clients, _from, state) do
    # Get only connected clients from ETS table
    connected_clients =
      get_all_client_records()
      |> Enum.filter(fn {_client_id, _client_pid, _mqtt_opts, _parse_state, status} ->
        status == @status_connected
      end)
      |> Enum.map(fn {client_id, _client_pid, mqtt_opts, _parse_state, status} ->
        # Extract MQTT version from mqtt_opts
        mqtt_version = extract_mqtt_version_from_opts(mqtt_opts)
        %{client_id: client_id, status: status, mqtt_version: mqtt_version}
      end)

    {:reply, connected_clients, state}
  end

  # Handle client connection when client is not already connected
  @spec handle_client_connection(map(), map(), client_id(), state()) ::
          {:reply, {:ok, client_id()} | {:error, error_reason(), error_message()}, state()}
  defp handle_client_connection(connection, broker, client_id, state) do
    try do
      case do_connect(connection, broker) do
        {:ok, client_pid, mqtt_opts} ->
          handle_successful_connection(client_id, client_pid, mqtt_opts, state)

        {:error, reason, error_message} ->
          handle_connection_error(client_id, reason, error_message, state)
      end
    catch
      :exit, {:shutdown, :tcp_closed} ->
        error_message = "Connection closed by broker before completing handshake"
        handle_connection_exit(client_id, :tcp_closed, error_message, state)

      :exit, reason ->
        error_message = extract_concise_error_message(reason)
        handle_connection_exit(client_id, reason, error_message, state)
    end
  end

  # Handle successful connection
  @spec handle_successful_connection(client_id(), client_pid(), keyword(), state()) ::
          {:reply, {:ok, client_id()}, state()}
  defp handle_successful_connection(client_id, client_pid, mqtt_opts, state) do
    # Monitor the client process
    Process.monitor(client_pid)

    # Store client info in ETS table
    store_client_record(client_id, client_pid, mqtt_opts, @status_connected)

    # Broadcast status change with connection timestamp
    # This will also broadcast the connection time since we updated the broadcast_status_change function
    broadcast_status_change(client_id, @status_connected)

    # Find the connection in the connection sets to get its topics
    connection_sets = Mqttable.ConnectionSets.get_all()
    connection = find_connection_by_client_id(connection_sets, client_id)

    # Resubscribe to saved topics if the connection is found
    if connection do
      resubscribe_to_saved_topics(connection, client_pid, state)
    end

    {:reply, {:ok, client_id}, state}
  end

  # Handle connection error
  @spec handle_connection_error(client_id(), error_reason(), error_message(), state()) ::
          {:reply, {:error, error_reason(), error_message()}, state()}
  defp handle_connection_error(client_id, reason, error_message, state) do
    Logger.error(
      "Failed to connect MQTT client #{client_id}: #{inspect(reason)} - #{error_message}"
    )

    # Broadcast the connection error
    broadcast_connection_error(client_id, error_message)

    # Update status to reconnecting in ETS table
    store_client_record(client_id, nil, nil, @status_reconnecting)

    # Broadcast status change
    broadcast_status_change(client_id, @status_reconnecting)

    # Return the error with both the reason and a human-readable message
    {:reply, {:error, reason, error_message}, state}
  end

  # Handle connection exit
  @spec handle_connection_exit(client_id(), error_reason(), error_message(), state()) ::
          {:reply, {:error, error_reason(), error_message()}, state()}
  defp handle_connection_exit(client_id, reason, error_message, state) do
    Logger.error("MQTT client #{client_id}: #{error_message}")

    # Broadcast the connection error
    broadcast_connection_error(client_id, error_message)

    # Update status to reconnecting in ETS table
    store_client_record(client_id, nil, nil, @status_reconnecting)

    # Broadcast status change
    broadcast_status_change(client_id, @status_reconnecting)

    # Return the error with both the reason and a human-readable message
    {:reply, {:error, reason, error_message}, state}
  end

  # Server callbacks - handle_info functions

  @impl true
  @spec handle_info({:DOWN, reference(), :process, pid(), term()}, state()) ::
          {:noreply, state()}
  def handle_info({:DOWN, _ref, :process, pid, reason}, state) do
    # Find the client_id for this pid
    client_id = find_client_id_by_pid(state, pid)

    if client_id do
      Logger.warning("MQTT client #{client_id} went down: #{inspect(reason)}")

      # Update client record to remove the pid but keep the client_id for reconnection
      # Set status to reconnecting and clear the pid
      store_client_record(client_id, nil, nil, @status_reconnecting)

      # Broadcast status change
      broadcast_status_change(client_id, @status_reconnecting)

      {:noreply, state}
    else
      {:noreply, state}
    end
  end

  @impl true
  @spec handle_info(:reconnect_saved_clients, state()) :: {:noreply, state()}
  def handle_info(:reconnect_saved_clients, state) do
    new_state =
      try do
        # Load connection sets from the ConnectionSets server
        connection_sets = Mqttable.ConnectionSets.get_all()

        # Process each connection set and reconnect clients that were previously connected
        reconnect_saved_clients(connection_sets, state)
      catch
        :exit, reason ->
          Logger.error("Exit while reconnecting saved clients: #{inspect(reason)}")
          state

        :error, reason ->
          Logger.error("Error while reconnecting saved clients: #{inspect(reason)}")
          state

        type, reason ->
          Logger.error(
            "Unexpected error while reconnecting saved clients: #{type} #{inspect(reason)}"
          )

          state
      end

    Process.send_after(self(), :reconnect_saved_clients, 10000)

    {:noreply, new_state}
  end

  @impl true
  @spec handle_info({:EXIT, pid(), term()}, state()) :: {:noreply, state()}
  def handle_info({:EXIT, pid, reason}, state) do
    # Find the client_id for this pid
    client_id = find_client_id_by_pid(state, pid)

    if client_id do
      Logger.warning("MQTT client #{client_id} exited: #{inspect(reason)}")

      # Update client record to remove the pid but keep the client_id for reconnection
      # Set status to reconnecting and clear the pid
      store_client_record(client_id, nil, nil, @status_reconnecting)

      # Broadcast status change to reconnecting
      broadcast_status_change(client_id, @status_reconnecting)

      Logger.info("Client #{client_id} will be reconnected by periodic check")

      {:noreply, state}
    else
      {:noreply, state}
    end
  end

  def handle_info({:publish, _}, state) do
    {:noreply, state}
  end

  def handle_info(msg, state) do
    Logger.info("Received unexpected message in MqttClient.Manager: #{inspect(msg)}")
    {:noreply, state}
  end

  # State management helper functions

  # Find client_id by pid
  @spec find_client_id_by_pid(state(), pid()) :: client_id() | nil
  defp find_client_id_by_pid(_state, pid) do
    # Use ETS match to find the client_id for a given pid
    case match_client_id_by_pid(pid) do
      [[client_id]] -> client_id
      [] -> nil
    end
  end

  # Reconnect clients that were previously connected
  @spec reconnect_saved_clients([map()], state()) :: state()
  defp reconnect_saved_clients(connection_sets, state) do
    # Process each connection set
    Enum.reduce(connection_sets, state, fn connection_set, acc_state ->
      # Process each connection in the set
      Enum.reduce(connection_set[:connections] || [], acc_state, fn connection, inner_acc_state ->
        client_id = connection.client_id

        # Only reconnect clients that are in reconnecting state or have status "reconnecting"
        # but are not currently connected according to our ETS table
        current_status = lookup_client_status(client_id)
        connection_status = connection[:status]

        should_reconnect =
          current_status == @status_reconnecting ||
            (connection_status == "reconnecting" && current_status != @status_connected) ||
            (current_status == @status_disconnected && connection_status == "connected")

        if should_reconnect do
          # Try to reconnect the client
          Logger.info("Attempting to reconnect client in reconnecting state: #{client_id}")
          reconnect_client(connection, connection_set, inner_acc_state)
        else
          # Skip connections that are already connected or disconnected
          inner_acc_state
        end
      end)
    end)
  end

  # Reconnect a single client
  @spec reconnect_client(map(), map(), state()) :: state()
  defp reconnect_client(connection, broker, state) do
    client_id = connection.client_id

    # Check if client is already connected
    case lookup_client_status(client_id) do
      @status_connected ->
        Logger.info("Client #{client_id} is already connected, skipping reconnection")
        state

      status when status in [@status_disconnected, @status_reconnecting] ->
        Logger.info("Attempting to reconnect saved client: #{client_id}")

        try do
          case do_connect(connection, broker) do
            {:ok, client_pid, mqtt_opts} ->
              # Monitor the client process
              Process.monitor(client_pid)

              # Store client info in ETS table
              store_client_record(client_id, client_pid, mqtt_opts, @status_connected)

              # Broadcast status change
              broadcast_status_change(client_id, @status_connected)

              # Resubscribe to saved topics
              resubscribe_to_saved_topics(connection, client_pid, state)

              state

            {:error, reason, error_message} ->
              Logger.warning(
                "Failed to reconnect saved client #{client_id}: #{inspect(reason)} - #{error_message}"
              )

              # Update status to reconnecting in ETS table for next periodic check
              store_client_record(client_id, nil, nil, @status_reconnecting)

              # Broadcast status change
              broadcast_status_change(client_id, @status_reconnecting)

              # Broadcast the connection error
              broadcast_connection_error(client_id, error_message)

              state
          end
        catch
          :exit, {:shutdown, :tcp_closed} ->
            error_message = "Connection closed by broker before completing handshake"
            handle_reconnect_exit(client_id, :tcp_closed, error_message, state)

          :exit, {:socket_closed_before_connack, reason} ->
            error_message = format_mqtt_error({:socket_closed_before_connack, reason})

            handle_reconnect_exit(
              client_id,
              {:socket_closed_before_connack, reason},
              error_message,
              state
            )

          :exit, reason ->
            error_message = extract_concise_error_message(reason)
            handle_reconnect_exit(client_id, reason, error_message, state)

          :error, reason ->
            error_message = "Error during reconnection: #{inspect(reason)}"
            handle_reconnect_exit(client_id, reason, error_message, state)

          type, reason ->
            error_message = "Unexpected error during reconnection: #{type} #{inspect(reason)}"
            handle_reconnect_exit(client_id, reason, error_message, state)
        end
    end
  end

  # Resubscribe to saved topics for a reconnected client
  @spec resubscribe_to_saved_topics(map(), client_pid(), state()) :: state()
  defp resubscribe_to_saved_topics(connection, client_pid, state) do
    client_id = connection.client_id
    topics = Map.get(connection, :topics, [])

    if topics != [] do
      Logger.info("Resubscribing client #{client_id} to #{length(topics)} saved topics")

      # Process each topic
      Enum.each(topics, fn topic_entry ->
        case topic_entry do
          # Handle string format (old format)
          topic when is_binary(topic) ->
            # For old format, use default QoS 0 and empty properties
            try_resubscribe(client_pid, client_id, topic, {[{:qos, 0}], %{}})

          # Handle map format with atom keys (new format)
          %{topic: topic_str} = topic_map when is_map(topic_map) ->
            # Extract subscription options and properties
            sub_opts = build_subscription_options(topic_map)
            try_resubscribe(client_pid, client_id, topic_str, sub_opts)

          # Handle map format with string keys (legacy format from JSON)
          topic_map when is_map(topic_map) and map_size(topic_map) > 0 ->
            # Try to get topic string from either atom or string key
            topic_str = Map.get(topic_map, :topic) || Map.get(topic_map, "topic")

            if topic_str && is_binary(topic_str) do
              # Convert string keys to atom keys if needed
              atom_keyed_map =
                if Map.has_key?(topic_map, "topic") do
                  for {key, val} <- topic_map, into: %{}, do: {String.to_atom(key), val}
                else
                  topic_map
                end

              # Extract subscription options and properties
              sub_opts = build_subscription_options(atom_keyed_map)
              try_resubscribe(client_pid, client_id, topic_str, sub_opts)
            else
              Logger.warning(
                "Skipping invalid topic entry for client #{client_id} (missing topic string): #{inspect(topic_map)}"
              )
            end

          # Skip invalid entries
          _ ->
            Logger.warning(
              "Skipping invalid topic entry for client #{client_id}: #{inspect(topic_entry)}"
            )
        end
      end)
    end

    # Return the state unchanged - we're just resubscribing, not modifying state
    state
  end

  # Helper function to prepare subscription properties with subscription identifier
  @spec prepare_subscription_properties(integer() | nil) :: map()
  defp prepare_subscription_properties(sub_id) do
    if sub_id && is_integer(sub_id) && sub_id > 0 do
      %{:"Subscription-Identifier" => sub_id}
    else
      %{}
    end
  end

  # Build subscription options and properties from topic map
  @spec build_subscription_options(map()) :: {keyword(), map()}
  defp build_subscription_options(topic_map) do
    # Build subscription options
    sub_opts = [
      {:qos, Map.get(topic_map, :qos, 0)}
    ]

    # Add optional parameters if they exist in the topic map
    sub_opts = add_option_if_exists(sub_opts, topic_map, :nl)
    sub_opts = add_option_if_exists(sub_opts, topic_map, :rap)
    sub_opts = add_option_if_exists(sub_opts, topic_map, :rh)

    # Process options to convert nl and rap values from 0/1 to false/true
    sub_opts = process_subscription_options(sub_opts)

    # Get subscription identifier if present
    sub_id = Map.get(topic_map, :id)

    # Prepare properties map with subscription identifier
    props = prepare_subscription_properties(sub_id)

    {sub_opts, props}
  end

  # Process subscription options to convert nl and rap values from 0/1 to false/true
  @spec process_subscription_options(keyword()) :: keyword()
  defp process_subscription_options(options) do
    Enum.map(options, fn
      {key, value} when key in [:nl, :rap] ->
        converted_value =
          case value do
            0 -> false
            1 -> true
            _ -> value
          end

        {key, converted_value}

      option ->
        option
    end)
  end

  # Helper function to add an option to the options list if it exists in the map
  @spec add_option_if_exists(keyword(), map(), atom()) :: keyword()
  defp add_option_if_exists(opts, map, key) do
    if Map.has_key?(map, key) do
      value = Map.get(map, key)
      [{key, value} | opts]
    else
      opts
    end
  end

  # Helper function to find a connection by client_id in the connection sets
  @spec find_connection_by_client_id([map()], client_id()) :: map() | nil
  defp find_connection_by_client_id(connection_sets, client_id) do
    # Iterate through all connection sets
    Enum.find_value(connection_sets, fn set ->
      # Find the connection with the matching client_id
      Enum.find(set[:connections] || [], fn conn ->
        Map.get(conn, :client_id) == client_id
      end)
    end)
  end

  # Try to resubscribe to a topic
  @spec try_resubscribe(client_pid(), client_id(), binary(), keyword() | {keyword(), map()}) ::
          :ok
  defp try_resubscribe(client_pid, client_id, topic, sub_opts) do
    # Validate topic before resubscribing
    case validate_topic_filter(topic) do
      :ok ->
        try do
          # Extract subscription options and properties
          {options, props} = sub_opts

          # Process subscription options to convert nl and rap values from 0/1 to false/true
          options = process_subscription_options(options)

          case :emqtt.subscribe(client_pid, props, [{topic, options}]) do
            {:ok, _props, reason_codes} ->
              # Check if the subscription was successful based on reason codes
              case check_subscription_success(reason_codes) do
                :ok ->
                  Logger.info("Successfully resubscribed client #{client_id} to topic #{topic}")
                  :ok

                {:error, error_message} ->
                  Logger.error(
                    "Failed to resubscribe client #{client_id} to topic #{topic}: #{error_message}"
                  )

                  :error
              end

            {:error, reason} ->
              error_message = format_mqtt_error(reason)

              Logger.error(
                "Failed to resubscribe client #{client_id} to topic #{topic}: #{error_message}"
              )

              :error
          end
        catch
          :exit, reason ->
            error_message =
              "Exit while resubscribing client #{client_id} to topic #{topic}: #{inspect(reason)}"

            Logger.error(error_message)
            :error
        end

      {:error, error_message} ->
        Logger.error("Topic validation failed for resubscription to #{topic}: #{error_message}")
        :error
    end
  end

  # Handle exit during reconnection
  @spec handle_reconnect_exit(client_id(), error_reason(), error_message(), state()) :: state()
  defp handle_reconnect_exit(client_id, _reason, error_message, state) do
    Logger.warning("Exit while reconnecting client #{client_id}: #{error_message}")

    # Update status to reconnecting in ETS table for next periodic check
    store_client_record(client_id, nil, nil, @status_reconnecting)

    # Broadcast status change
    broadcast_status_change(client_id, @status_reconnecting)

    # Broadcast the connection error
    broadcast_connection_error(client_id, error_message)

    state
  end

  # MQTT connection functions

  # Connect to MQTT broker
  @spec do_connect(map(), map()) ::
          {:ok, client_pid(), keyword()}
          | {:error, error_reason(), error_message()}
  defp do_connect(connection, broker) do
    # Prepare connection parameters
    connection = ConnectionHelpers.prepare_connection_for_mqtt(connection)

    # Build MQTT options
    mqtt_opts = build_mqtt_options(connection, broker)

    # Start MQTT client
    case :emqtt.start_link(mqtt_opts) do
      {:ok, client_pid} ->
        connect_to_broker(client_pid, broker, mqtt_opts)

      {:error, reason} ->
        error_message = format_mqtt_error(reason)
        Logger.error("Failed to start MQTT client: #{inspect(reason)} - #{error_message}")
        {:error, reason, error_message}
    end
  end

  # Build MQTT connection options
  @spec build_mqtt_options(map(), map()) :: keyword()
  defp build_mqtt_options(connection, broker) do
    # Base options
    base_opts = [
      host: String.to_charlist(broker.host),
      port: String.to_integer(broker.port),
      clientid: connection.client_id,
      clean_start: connection.clean_start,
      keepalive: connection.keep_alive,
      proto_ver: mqtt_version_to_proto_ver(connection.mqtt_version)
    ]

    # Add authentication if provided
    auth_opts = add_authentication_options(connection, base_opts)

    # Add protocol-specific options
    transport_opts = add_protocol_options(broker.protocol, auth_opts)

    # Add MQTT version-specific options
    add_mqtt_version_options(connection, transport_opts)
  end

  # Convert MQTT version string to protocol version atom
  @spec mqtt_version_to_proto_ver(String.t()) :: atom()
  defp mqtt_version_to_proto_ver(mqtt_version) do
    case mqtt_version do
      "5.0" -> :v5
      "3.1.1" -> :v4
      "3.1" -> :v3
      # Default to MQTT 3.1.1 if version is not recognized
      _ -> :v4
    end
  end

  # Extract MQTT version string from mqtt_opts
  @spec extract_mqtt_version_from_opts(keyword() | nil) :: String.t()
  defp extract_mqtt_version_from_opts(nil), do: "5.0"

  defp extract_mqtt_version_from_opts(mqtt_opts) when is_list(mqtt_opts) do
    case Keyword.get(mqtt_opts, :proto_ver, :v5) do
      :v5 -> "5.0"
      :v4 -> "3.1.1"
      :v3 -> "3.1"
      _ -> "5.0"
    end
  end

  defp extract_mqtt_version_from_opts(_), do: "5.0"

  # Add MQTT version-specific options
  @spec add_mqtt_version_options(map(), keyword()) :: keyword()
  defp add_mqtt_version_options(connection, opts) do
    case connection.mqtt_version do
      "5.0" ->
        # Add MQTT 5.0 specific properties
        properties = build_mqtt5_properties(connection)

        if map_size(properties) > 0 do
          [{:properties, properties} | opts]
        else
          opts
        end

      _ ->
        # For MQTT 3.1.1 and 3.1, no additional properties needed
        opts
    end
  end

  # Build MQTT 5.0 specific properties
  @spec build_mqtt5_properties(map()) :: map()
  defp build_mqtt5_properties(connection) do
    properties = %{}

    # Add Session Expiry Interval if set
    properties =
      if is_integer(connection.session_expiry_interval) && connection.session_expiry_interval > 0 do
        Map.put(properties, :"Session-Expiry-Interval", connection.session_expiry_interval)
      else
        properties
      end

    # Add Receive Maximum if set
    properties =
      if is_integer(connection.receive_maximum) && connection.receive_maximum > 0 do
        Map.put(properties, :"Receive-Maximum", connection.receive_maximum)
      else
        properties
      end

    # Add Maximum Packet Size if set
    properties =
      if is_integer(connection.maximum_packet_size) && connection.maximum_packet_size > 0 do
        Map.put(properties, :"Maximum-Packet-Size", connection.maximum_packet_size)
      else
        properties
      end

    # Add Topic Alias Maximum if set
    properties =
      if is_integer(connection.topic_alias_maximum) && connection.topic_alias_maximum > 0 do
        Map.put(properties, :"Topic-Alias-Maximum", connection.topic_alias_maximum)
      else
        properties
      end

    # Add Request Response Information if true
    properties =
      if connection.request_response_info do
        Map.put(properties, :"Request-Response-Information", 1)
      else
        properties
      end

    # Add Request Problem Information if true
    properties =
      if connection.request_problem_info do
        Map.put(properties, :"Request-Problem-Information", 1)
      else
        properties
      end

    # Add User Properties if any
    properties =
      if connection.user_properties && is_list(connection.user_properties) &&
           length(connection.user_properties) > 0 do
        valid_props =
          connection.user_properties
          |> Enum.filter(fn prop ->
            is_map(prop) && Map.has_key?(prop, :key) && Map.has_key?(prop, :value) &&
              prop.key != "" && prop.value != ""
          end)
          |> Enum.map(fn prop -> {prop.key, prop.value} end)

        if length(valid_props) > 0 do
          Map.put(properties, :"User-Property", valid_props)
        else
          properties
        end
      else
        properties
      end

    properties
  end

  # Add authentication options if provided
  @spec add_authentication_options(map(), keyword()) :: keyword()
  defp add_authentication_options(connection, opts) do
    # Add username if provided
    opts =
      if connection.username && connection.username != "" do
        [{:username, connection.username} | opts]
      else
        opts
      end

    # Add password if provided
    opts =
      if connection.password && connection.password != "" do
        [{:password, connection.password} | opts]
      else
        opts
      end

    # Add will message if topic is provided
    if connection.will_topic && connection.will_topic != "" do
      add_will_message(connection, opts)
    else
      opts
    end
  end

  # Add will message options
  @spec add_will_message(map(), keyword()) :: keyword()
  defp add_will_message(connection, opts) do
    # Base will options
    will_opts = [
      will_topic: connection.will_topic,
      will_payload: connection.will_payload || "",
      will_qos: String.to_integer(connection.will_qos || "0"),
      will_retain: connection.will_retain
    ]

    # Add will options to the main options
    opts = Keyword.merge(opts, will_opts)

    # Add MQTT 5.0 specific will properties if using MQTT 5.0
    if connection.mqtt_version == "5.0" do
      will_props = build_mqtt5_will_properties(connection)

      if map_size(will_props) > 0 do
        [{:will_props, will_props} | opts]
      else
        opts
      end
    else
      opts
    end
  end

  # Build MQTT 5.0 specific will properties
  @spec build_mqtt5_will_properties(map()) :: map()
  defp build_mqtt5_will_properties(connection) do
    properties = %{}

    # Add Will Delay Interval if set
    properties =
      if is_integer(connection.will_delay_interval) && connection.will_delay_interval > 0 do
        Map.put(properties, :"Will-Delay-Interval", connection.will_delay_interval)
      else
        properties
      end

    # Add Payload Format Indicator if set
    properties =
      if connection.will_payload_format do
        Map.put(properties, :"Payload-Format-Indicator", 1)
      else
        properties
      end

    # Add Message Expiry Interval if set
    properties =
      if is_integer(connection.will_message_expiry) && connection.will_message_expiry > 0 do
        Map.put(properties, :"Message-Expiry-Interval", connection.will_message_expiry)
      else
        properties
      end

    # Add Content Type if set
    properties =
      if connection.will_content_type && connection.will_content_type != "" do
        Map.put(properties, :"Content-Type", connection.will_content_type)
      else
        properties
      end

    # Add Response Topic if set
    properties =
      if connection.will_response_topic && connection.will_response_topic != "" do
        Map.put(properties, :"Response-Topic", connection.will_response_topic)
      else
        properties
      end

    # Add Correlation Data if set
    properties =
      if connection.will_correlation_data && connection.will_correlation_data != "" do
        Map.put(properties, :"Correlation-Data", connection.will_correlation_data)
      else
        properties
      end

    properties
  end

  # Add protocol-specific options
  @spec add_protocol_options(String.t(), keyword()) :: keyword()
  defp add_protocol_options("mqtts", opts), do: [{:ssl, true} | opts]
  defp add_protocol_options("wss", opts), do: [{:ssl, true}, {:ws_path, "/mqtt"} | opts]
  defp add_protocol_options("ws", opts), do: [{:ws_path, "/mqtt"} | opts]
  defp add_protocol_options(_, opts), do: opts

  # Connect to broker with the client
  @spec connect_to_broker(client_pid(), map(), keyword()) ::
          {:ok, client_pid(), keyword()}
          | {:error, error_reason(), error_message()}
  defp connect_to_broker(client_pid, broker, mqtt_opts) do
    try do
      case :emqtt.connect(client_pid) do
        {:ok, _props} ->
          Logger.info("Connected to MQTT broker: #{broker.host}:#{broker.port}")
          {:ok, client_pid, mqtt_opts}

        {:error, reason} ->
          handle_connect_error(client_pid, reason)
      end
    catch
      :exit, {:shutdown, :tcp_closed} ->
        handle_connect_exit(client_pid, :tcp_closed)

      :exit, {:socket_closed_before_connack, reason} ->
        handle_connect_exit(client_pid, {:socket_closed_before_connack, reason})

      :exit, reason ->
        handle_connect_exit(client_pid, reason)
    end
  end

  # Handle connection error
  @spec handle_connect_error(client_pid(), error_reason()) ::
          {:error, error_reason(), error_message()}
  defp handle_connect_error(client_pid, reason) do
    error_message = format_mqtt_error(reason)

    Logger.error("Failed to connect to MQTT broker: #{inspect(reason)} - #{error_message}")

    # Only try to stop if the process is still alive
    if Process.alive?(client_pid) do
      safe_stop(client_pid)
    end

    {:error, reason, error_message}
  end

  # Handle connection exit
  @spec handle_connect_exit(client_pid(), error_reason()) ::
          {:error, error_reason(), error_message()}
  defp handle_connect_exit(client_pid, {:shutdown, :tcp_closed}) do
    error_message = format_mqtt_error({:shutdown, :tcp_closed})
    Logger.error("Failed to connect to MQTT broker: tcp_closed - #{error_message}")

    # Only try to stop if the process is still alive
    if Process.alive?(client_pid) do
      safe_stop(client_pid)
    end

    {:error, :tcp_closed, error_message}
  end

  defp handle_connect_exit(client_pid, {:socket_closed_before_connack, reason}) do
    error_message = format_mqtt_error({:socket_closed_before_connack, reason})

    Logger.error(
      "Failed to connect to MQTT broker: socket_closed_before_connack - #{error_message}"
    )

    # Only try to stop if the process is still alive
    if Process.alive?(client_pid) do
      safe_stop(client_pid)
    end

    {:error, :socket_closed_before_connack, error_message}
  end

  defp handle_connect_exit(client_pid, reason) do
    error_message = extract_concise_error_message(reason)

    Logger.error("Failed to connect to MQTT broker with unexpected exit: #{error_message}")

    # Only try to stop if the process is still alive
    if Process.alive?(client_pid) do
      safe_stop(client_pid)
    end

    {:error, reason, error_message}
  end

  # Error handling functions

  # Format MQTT error codes into human-readable messages
  @spec format_mqtt_error(error_reason()) :: error_message()
  defp format_mqtt_error(reason) do
    case reason do
      :econnrefused ->
        "Connection refused: The broker is not reachable or not running"

      :timeout ->
        "Connection timeout: The broker did not respond in time"

      :nxdomain ->
        "Host not found: The broker hostname could not be resolved"

      :econnreset ->
        "Connection reset: The connection was forcibly closed by the broker"

      :closed ->
        "Connection closed: The connection was closed unexpectedly"

      :etimedout ->
        "Connection timed out: The operation timed out"

      :ehostunreach ->
        "Host unreachable: The broker host is unreachable"

      :enetunreach ->
        "Network unreachable: The network is unreachable"

      :eacces ->
        "Permission denied: Access to the broker was denied"

      :einval ->
        "Invalid argument: One of the connection parameters is invalid"

      :badarg ->
        "Bad argument: One of the connection parameters is invalid"

      :already_present ->
        "Client ID already in use: Another client with the same ID is already connected"

      :not_authorized ->
        "Not authorized: Authentication failed, check your username and password"

      :bad_username_or_password ->
        "Bad username or password: Authentication failed, check your credentials"

      :protocol_error ->
        "Protocol error: There was an error in the MQTT protocol"

      :unknown_ca ->
        "Unknown CA: The server's certificate authority is unknown"

      :cert_verify_failed ->
        "Certificate verification failed: The server's certificate could not be verified"

      :tcp_closed ->
        "Connection closed: The broker closed the connection before completing the handshake"

      {:shutdown, :tcp_closed} ->
        "Connection closed: The broker closed the connection before completing the handshake"

      {:socket_closed_before_connack, _} ->
        "Connection closed: The broker closed the connection before completing the handshake"

      _ ->
        "Connection error: #{inspect(reason)}"
    end
  end

  # MQTT disconnection functions

  # Disconnect from MQTT broker
  @spec do_disconnect(client_pid()) :: :ok
  defp do_disconnect(client_pid) do
    try do
      disconnect_if_alive(client_pid)
    rescue
      e ->
        Logger.error("Error disconnecting MQTT client: #{inspect(e)}")
        # Still return :ok since the client is effectively disconnected
        :ok
    catch
      :exit, reason ->
        Logger.error("Exit while disconnecting MQTT client: #{inspect(reason)}")
        # Still return :ok since the client is effectively disconnected
        :ok
    end
  end

  # Disconnect if the process is still alive
  @spec disconnect_if_alive(client_pid()) :: :ok
  defp disconnect_if_alive(client_pid) do
    if Process.alive?(client_pid) do
      # Try to disconnect gracefully first
      _ = safe_disconnect(client_pid)

      # Try to stop the process
      _ = safe_stop(client_pid)

      :ok
    else
      # Process is already dead, so consider it successfully disconnected
      Logger.info("MQTT client process is already terminated")
      :ok
    end
  end

  # Safely disconnect the client
  @spec safe_disconnect(client_pid()) :: :ok
  defp safe_disconnect(client_pid) do
    try do
      :emqtt.disconnect(client_pid)
    catch
      :exit, _ ->
        # If disconnect fails, that's okay, we'll still try to stop the process
        :ok
    end
  end

  # Safely stop the client
  @spec safe_stop(client_pid()) :: :ok
  defp safe_stop(client_pid) do
    # Double check if process is alive before attempting to stop
    if Process.alive?(client_pid) do
      try do
        :emqtt.stop(client_pid)
      rescue
        _error ->
          # Any other error, continue silently
          :ok
      catch
        :exit, {:noproc, _} ->
          # Process already terminated, that's fine
          :ok

        :exit, reason ->
          # Other exit reasons, log but continue
          Logger.debug(
            "Exit while stopping MQTT client #{inspect(client_pid)}: #{inspect(reason)}"
          )

          :ok
      end
    else
      # Process is already dead
      Logger.debug("MQTT client process #{inspect(client_pid)} is already dead")
      :ok
    end
  end

  # PubSub functions

  # Broadcast status change
  @spec broadcast_status_change(client_id(), client_status()) :: :ok | {:error, term()}
  defp broadcast_status_change(client_id, status) do
    # Broadcast the status change
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_status_changed, client_id, status}
    )

    # If the status is connected, also broadcast the connection time
    if status == @status_connected do
      PubSub.broadcast(
        Mqttable.PubSub,
        @pubsub_topic,
        {:mqtt_client_connection_time, client_id, DateTime.utc_now()}
      )
    end
  end

  # Broadcast connection error
  @spec broadcast_connection_error(client_id(), error_message()) :: :ok | {:error, term()}
  defp broadcast_connection_error(client_id, error_message) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_connection_error, client_id, error_message}
    )
  end

  defp broadcast_topic_subscription(client_id, topic, opts, sub_id) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_topic_subscribed, client_id, topic, opts, sub_id}
    )
  end

  # Broadcast topic unsubscription
  @spec broadcast_topic_unsubscription(client_id(), binary()) :: :ok | {:error, term()}
  defp broadcast_topic_unsubscription(client_id, topic) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_topic_unsubscribed, client_id, topic}
    )
  end

  # Check if subscription was successful based on reason codes
  @spec check_subscription_success(list()) :: :ok | {:error, error_message()}
  defp check_subscription_success(reason_codes) do
    # Process each reason code
    Enum.reduce_while(reason_codes, :ok, fn reason_code, _acc ->
      case reason_code do
        # MQTT v3.1.1 and v5.0 success codes (0x00, 0x01, 0x02)
        code when code in [0, 1, 2] ->
          {:cont, :ok}

        # MQTT v5.0 and v3.1.1 error codes
        0x80 ->
          {:halt, {:error, "Subscription failed: Unspecified error"}}

        0x83 ->
          {:halt, {:error, "Implementation specific error"}}

        0x87 ->
          {:halt, {:error, "Not authorized"}}

        0x8F ->
          {:halt, {:error, "Topic filter invalid"}}

        0x91 ->
          {:halt, {:error, "Packet identifier in use"}}

        0x97 ->
          {:halt, {:error, "Quota exceeded"}}

        0xA1 ->
          {:halt, {:error, "Shared subscriptions not supported"}}

        0xA2 ->
          {:halt, {:error, "Wildcard subscriptions not supported"}}

        # Any other code is treated as an error
        _ ->
          {:halt, {:error, "Unknown error code: #{inspect(reason_code)}"}}
      end
    end)
  end

  # Helper function to extract a concise error message from complex error reasons
  @spec extract_concise_error_message(term()) :: error_message()
  defp extract_concise_error_message(reason) do
    cond do
      # Handle socket_closed_before_connack errors
      is_socket_closed_before_connack_error?(reason) ->
        "Connection closed by broker before completing handshake"

      # Handle tcp_closed errors
      is_tcp_closed_error?(reason) ->
        "Connection closed by broker before completing handshake"

      # Handle GenServer call errors with nested reasons
      is_nested_error?(reason) ->
        extract_nested_error_message(reason)

      # Default case for simple errors
      true ->
        "Connection error: #{inspect(reason)}"
    end
  end

  # Topic validation helper function
  @spec validate_topic_filter(binary()) :: :ok | {:error, error_message()}
  defp validate_topic_filter(topic) do
    try do
      Topic.validate({:filter, topic})
      :ok
    rescue
      e in RuntimeError ->
        error_message = "Invalid topic filter: #{e.message}"
        Logger.error("Topic validation failed for #{topic}: #{error_message}")
        {:error, error_message}
    catch
      :error, reason ->
        error_message = "Invalid topic filter: #{inspect(reason)}"
        Logger.error("Topic validation failed for #{topic}: #{error_message}")
        {:error, error_message}
    end
  end

  # Helper functions to encapsulate all ETS operations for consistency

  # Insert client record into ETS table
  defp store_client_record(client_id, client_pid, mqtt_opts, status) do
    # Handle nil mqtt_opts by providing defaults
    opts = mqtt_opts || []

    max_size = Map.get(Keyword.get(opts, :properties, %{}), :"Receive-Maximum", 0xFFFFFFF)

    ver =
      case Keyword.get(opts, :proto_ver, :v5) do
        :v5 -> 5
        _ -> 4
      end

    parse_state = :emqtt_frame.initial_parse_state(%{max_size: max_size, version: ver})
    :ets.insert(__MODULE__, {client_id, client_pid, opts, parse_state, status})
  end

  defp remove_client_record(client_id) do
    :ets.delete(__MODULE__, client_id)
  end

  defp lookup_client_status(client_id) do
    case :ets.lookup(__MODULE__, client_id) do
      [{^client_id, _client_pid, _mqtt_opts, _, status}] -> status
      [] -> @status_disconnected
    end
  end

  def lookup_client_parse_state(client_id) do
    case :ets.lookup(__MODULE__, client_id) do
      [{^client_id, _, _, parse_state, _}] -> parse_state
      [] -> :emqtt_frame.initial_parse_state(%{max_size: 0xFFFFFFF, version: 5})
    end
  end

  def store_client_parse_state(client_id, parse_state) do
    :ets.update_element(__MODULE__, client_id, {4, parse_state})
  end

  defp lookup_client_record(client_id) do
    case :ets.lookup(__MODULE__, client_id) do
      [{^client_id, client_pid, mqtt_opts, _, status}] ->
        {status, client_pid, mqtt_opts}

      [] ->
        {@status_disconnected, nil, nil}
    end
  end

  # Match client_id by pid
  @spec match_client_id_by_pid(pid()) :: [[client_id()]] | []
  defp match_client_id_by_pid(pid) do
    :ets.match(__MODULE__, {:"$1", pid, :_, :_, :_})
  end

  defp get_all_client_records() do
    :ets.tab2list(__MODULE__)
  end

  # Check if the reason is a socket_closed_before_connack error
  @spec is_socket_closed_before_connack_error?(term()) :: boolean()
  defp is_socket_closed_before_connack_error?(reason) do
    is_tuple(reason) && tuple_size(reason) >= 2 &&
      elem(reason, 0) == :socket_closed_before_connack
  end

  # Check if the reason is a tcp_closed error
  @spec is_tcp_closed_error?(term()) :: boolean()
  defp is_tcp_closed_error?(reason) do
    is_tuple(reason) && tuple_size(reason) >= 2 &&
      elem(reason, 0) == :shutdown && elem(reason, 1) == :tcp_closed
  end

  # Check if the reason is a nested error
  @spec is_nested_error?(term()) :: boolean()
  defp is_nested_error?(reason) do
    is_tuple(reason) && tuple_size(reason) >= 2 && is_tuple(elem(reason, 0))
  end

  # Extract message from nested error
  @spec extract_nested_error_message(tuple()) :: error_message()
  defp extract_nested_error_message(reason) do
    inner_reason = elem(reason, 0)

    if is_tuple(inner_reason) && tuple_size(inner_reason) >= 2 do
      case elem(inner_reason, 0) do
        :shutdown ->
          case elem(inner_reason, 1) do
            :tcp_closed -> "Connection closed by broker before completing handshake"
            other -> "Connection error: #{inspect(other)}"
          end

        other ->
          "Connection error: #{inspect(other)}"
      end
    else
      "Connection error: #{inspect(inner_reason)}"
    end
  end
end
